# Contact Form with Mailtrap Integration

This document explains how the contact form is integrated with Mailtrap for email sending.

## Setup

### 1. Environment Variables

The following environment variables are configured in `.env.local`:

```env
# Mailtrap Configuration
MAILTRAP_HOST=sandbox.smtp.mailtrap.io
MAILTRAP_PORT=2525
MAILTRAP_USER=18b92eead9dd8c
MAILTRAP_PASS=****d20f

# Email Configuration
CONTACT_EMAIL_TO=<EMAIL>
CONTACT_EMAIL_FROM=<EMAIL>
```

**Important**: Replace `****d20f` with your actual Mailtrap password.

### 2. Dependencies

The following packages were installed:
- `nodemailer` - For sending emails
- `@types/nodemailer` - TypeScript types for nodemailer

## How it Works

### 1. Contact Form (`app/contact-us/page.tsx`)

The contact form is a client-side React component that:
- Collects user input (name, email, phone, subject, message)
- Validates required fields
- Sends form data to the API endpoint
- Shows success/error messages
- Disables the submit button during submission

### 2. API Route (`app/api/contact/route.ts`)

The API route handles form submissions:
- Validates incoming data
- Creates an HTML email with Arabic content
- Sends email via Mailtrap SMTP
- Returns appropriate success/error responses

### 3. Email Template

The email includes:
- Sender information (name, email, phone)
- Subject (if provided)
- Message content (if provided)
- Timestamp in Saudi Arabia timezone
- RTL (right-to-left) formatting for Arabic text

## Testing

1. Start the development server:
   ```bash
   npm run dev
   ```

2. Navigate to `/contact-us`

3. Fill out the form and submit

4. Check your Mailtrap inbox for the email

## Features

- ✅ Form validation (required fields, email format)
- ✅ Loading states and user feedback
- ✅ Arabic language support
- ✅ Responsive design
- ✅ Phone number input with country selection
- ✅ HTML and plain text email formats
- ✅ Environment variable configuration
- ✅ Error handling

## Customization

### Changing Email Recipients

Update the `CONTACT_EMAIL_TO` environment variable in `.env.local`.

### Modifying Email Template

Edit the `mailOptions` object in `app/api/contact/route.ts` to customize the email format.

### Adding More Form Fields

1. Update the `FormData` interface in `app/contact-us/page.tsx`
2. Add the new input fields to the form
3. Update the API route to handle the new fields
4. Modify the email template to include the new data

## Production Deployment

For production:
1. Replace Mailtrap with a production email service (SendGrid, AWS SES, etc.)
2. Update environment variables with production credentials
3. Consider adding rate limiting to prevent spam
4. Add CAPTCHA for additional security
