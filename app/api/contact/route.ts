import { NextRequest, NextResponse } from 'next/server';
import nodemailer from 'nodemailer';

// Create transporter with Mailtrap configuration from environment variables
const transporter = nodemailer.createTransport({
  host: process.env.MAILTRAP_HOST,
  port: parseInt(process.env.MAILTRAP_PORT || '2525'),
  auth: {
    user: process.env.MAILTRAP_USER,
    pass: process.env.MAILTRAP_PASS
  }
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, email, phone, subject, message } = body;

    // Basic validation
    if (!name || !email || !phone) {
      return NextResponse.json(
        { error: 'الاسم والبريد الإلكتروني ورقم الجوال مطلوبة' },
        { status: 400 }
      );
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'يرجى إدخال بريد إلكتروني صحيح' },
        { status: 400 }
      );
    }

    // Create email content
    const mailOptions = {
      from: `"GT Website Contact Form" <${process.env.CONTACT_EMAIL_FROM}>`,
      to: process.env.CONTACT_EMAIL_TO, // The email where you want to receive contact form submissions
      subject: `رسالة جديدة من موقع GT: ${subject || 'بدون موضوع'}`,
      html: `
        <div dir="rtl" style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #5840BA; border-bottom: 2px solid #5840BA; padding-bottom: 10px;">
            رسالة جديدة من موقع GT
          </h2>
          
          <div style="background-color: #f9f9f9; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #333; margin-top: 0;">معلومات المرسل:</h3>
            <p><strong>الاسم:</strong> ${name}</p>
            <p><strong>البريد الإلكتروني:</strong> ${email}</p>
            <p><strong>رقم الجوال:</strong> ${phone}</p>
            ${subject ? `<p><strong>موضوع الاستفسار:</strong> ${subject}</p>` : ''}
          </div>

          ${message ? `
            <div style="background-color: #fff; padding: 20px; border: 1px solid #ddd; border-radius: 8px;">
              <h3 style="color: #333; margin-top: 0;">الرسالة:</h3>
              <p style="line-height: 1.6; white-space: pre-wrap;">${message}</p>
            </div>
          ` : ''}

          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; font-size: 12px;">
            <p>تم إرسال هذه الرسالة من نموذج التواصل في موقع GT</p>
            <p>التاريخ والوقت: ${new Date().toLocaleString('ar-SA', { timeZone: 'Asia/Riyadh' })}</p>
          </div>
        </div>
      `,
      text: `
رسالة جديدة من موقع GT

معلومات المرسل:
الاسم: ${name}
البريد الإلكتروني: ${email}
رقم الجوال: ${phone}
${subject ? `موضوع الاستفسار: ${subject}` : ''}

${message ? `الرسالة:\n${message}` : ''}

تم إرسال هذه الرسالة من نموذج التواصل في موقع GT
التاريخ والوقت: ${new Date().toLocaleString('ar-SA', { timeZone: 'Asia/Riyadh' })}
      `
    };

    // Send email
    await transporter.sendMail(mailOptions);

    return NextResponse.json(
      { message: 'تم إرسال رسالتك بنجاح. سنتواصل معك قريباً.' },
      { status: 200 }
    );

  } catch (error) {
    console.error('Error sending email:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى.' },
      { status: 500 }
    );
  }
}
