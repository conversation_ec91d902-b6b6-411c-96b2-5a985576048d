"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { ArrowUpRight, Instagram, Linkedin, Mail, MapPin, Phone } from "lucide-react";
import React, { useState } from "react";
import PhoneInput from "./components/PhoneInput";
import Link from "next/link";

interface FormData {
  name: string;
  email: string;
  phone: string;
  subject: string;
  message: string;
}

const ContactPage = () => {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<{
    type: 'success' | 'error' | null;
    message: string;
  }>({ type: null, message: '' });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handlePhoneChange = (phone: string) => {
    setFormData(prev => ({
      ...prev,
      phone
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus({ type: null, message: '' });

    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (response.ok) {
        setSubmitStatus({
          type: 'success',
          message: data.message
        });
        // Reset form
        setFormData({
          name: '',
          email: '',
          phone: '',
          subject: '',
          message: ''
        });
      } else {
        setSubmitStatus({
          type: 'error',
          message: data.error || 'حدث خطأ أثناء إرسال الرسالة'
        });
      }
    } catch (error) {
      setSubmitStatus({
        type: 'error',
        message: 'حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.'
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  return (
    <div className=" mx-auto pt-30 container ">
      <div className="flex  flex-col md:flex-row gap-20">
        {/* Contact Form */}
        <div className="flex-2">
          <div className="bg-white rounded-lg  p-8">
            <h1 className="text-3xl font-bold text-[#161616] mb-2">
              تواصل معنا
            </h1>
            <p className="text-[#161616] font-[400] text-base mb-8">
              مثال على وصف الخدمة لدينا فقط أريد بناء موقعك، نطلب ذلك تعبئة
              عملية البناء والطريق في مكتب التوثيق أو خدمات كاتب العدل لتسجيل
              العقار بسلاسة.
            </p>

            {/* Status Messages */}
            {submitStatus.type && (
              <div className={`p-4 rounded-lg mb-6 ${
                submitStatus.type === 'success'
                  ? 'bg-green-50 border border-green-200 text-green-800'
                  : 'bg-red-50 border border-red-200 text-red-800'
              }`}>
                {submitStatus.message}
              </div>
            )}

            <form className="space-y-6" onSubmit={handleSubmit}>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الاسم *
                </label>
                <Input
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="ادخل الاسم"
                  className="w-full shadow-none bg-[#F9FAFB] border border-[#D1D5DB]"
                  required
                />
              </div>

              <div className="flex gap-4 w-full">
                <div className="w-full">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    البريد الالكتروني *
                  </label>
                  <Input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    placeholder="ادخل البريد الالكتروني"
                    className="w-full shadow-none bg-[#F9FAFB] border border-[#D1D5DB]"
                    required
                  />
                </div>
                <div className="w-full">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    رقم الجوال *
                  </label>
                  <div className="flex">
                    <PhoneInput
                      value={formData.phone}
                      onChange={handlePhoneChange}
                    />
                  </div>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  موضوع الاستفسار{" "}
                </label>
                <Input
                  name="subject"
                  value={formData.subject}
                  onChange={handleInputChange}
                  placeholder="ادخل موضوع الاستفسار"
                  className="w-full shadow-none bg-[#F9FAFB] border border-[#D1D5DB]"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  كيف يمكننا المساعدة؟
                </label>
                <Textarea
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  placeholder="يمكنك كتابة ما تريد مساعدتك به هنا..."
                  rows={4}
                  className="w-full min-h-[125px] shadow-none bg-[#F9FAFB] border border-[#D1D5DB]"
                />
              </div>

              <div className="w-full flex justify-end">
                <Button
                  type="submit"
                  size="lg"
                  disabled={isSubmitting}
                  className="bg-[#5840BA] text-[#FFF] rounded-3xl px-[8px] md:px-[16px] py-[8px] md:py-[8px] text-sm md:text-[15px] font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? 'جاري الإرسال...' : 'إرسال'}
                  <ArrowUpRight className="w-6 h-6 md:w-7 md:h-7 ml-2" />
                </Button>
              </div>
            </form>
          </div>
        </div>
        {/* Contact Information Sidebar */}
        <div className="flex-1">
          <div className="bg-white rounded-lg  border border-[#D2D6DB] max-w-sm p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-6">
              بيانات الاتصال
            </h2>

            <div className="space-y-6">
              <div className="flex items-start">
                <Phone className="w-5 h-5 text-[#5840BA] mt-1 ml-3" />
                <div>
                  <h3 className="font-medium text-gray-900 mb-1">رقم الجوال</h3>
                  <p className="text-[#5840BA] text-base">+966 55 150 7434</p>
                </div>
              </div>

              <div className="flex items-start">
                <Mail className="w-5 h-5 text-[#5840BA] mt-1 ml-3" />
                <div>
                  <h3 className="font-medium text-gray-900 mb-1">
                    البريد الالكتروني
                  </h3>
                  <p className="text-[#5840BA] text-base"><EMAIL></p>
                </div>
              </div>

              <div className="flex items-start">
                <MapPin className="w-5 h-5 text-[#5840BA] mt-1 ml-3" />
                <div>
                  <h3 className="font-medium text-gray-900 mb-1">الموقع</h3>
                  <p className="text-[#5840BA] text-base leading-relaxed">
                    مبنى رقم 7266، طريق الملك فهد، 4405 حي العليا، 12331 الرياض.
                  </p>
                </div>
              </div>
            </div>

            <div className="mt-8">
              <p className="text-sm text-gray-600 mb-4">
                تابع آخر أخبارنا على شبكات التواصل
              </p>
              <div className="flex gap-3">
                {/* Instagram */}
                <Link
                  href="#"
                  className="w-8 h-8 border-1 border-[#ECECECE5] bg-white rounded-[9px] flex items-center justify-center  hover:bg-gray-50 transition-colors"
                >
                  <Instagram />
                </Link>
                {/* X (Twitter) */}
                <Link
                  href="#"
                  className="w-8 h-8 border-1 border-[#ECECECE5]  bg-white rounded-[9px] flex items-center justify-center  hover:bg-gray-50 transition-colors"
                >
                  <svg
                    className="w-6 h-6 text-black text-sm font-[400]"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
                  </svg>
                </Link>

                {/* LinkedIn */}
                <Link
                  href="#"
                  className="w-8 h-8 border-1 border-[#ECECECE5] bg-white rounded-[9px] flex items-center justify-center  hover:bg-gray-50 transition-colors"
                >
                  <Linkedin />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContactPage;
